import os
import gc
import torch
import base64
import filetype
import tempfile
import shutil
from pathlib import Path
from typing import Optional, Dict, Any, Tuple, Union, List

from logger import get_logger

logger = get_logger("utils")

def clean_memory() -> None:
    """
    清理内存和GPU缓存
    """
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
    gc.collect()
    logger.debug("内存清理完成")

def decode_base64(base64_str: str) -> bytes:
    """
    解码Base64字符串为字节数据
    
    Args:
        base64_str: Base64编码的字符串
        
    Returns:
        bytes: 解码后的字节数据
    """
    try:
        return base64.b64decode(base64_str)
    except Exception as e:
        logger.error(f"Base64解码失败: {str(e)}")
        raise ValueError(f"无效的Base64编码: {str(e)}")

def detect_file_type(file_bytes: bytes, filename: Optional[str] = None) -> str:
    """
    检测文件类型
    
    Args:
        file_bytes: 文件字节内容
        filename: 可选的文件名，用于从文件扩展名推断类型
        
    Returns:
        str: 文件类型（扩展名，不含点，如'docx'）
    """
    # 使用filetype库检测
    detected_type = filetype.guess_extension(file_bytes)
    
    if detected_type:
        logger.info(f"检测到文件类型: {detected_type}")
        return detected_type
    
    # 如果无法检测，尝试从文件名获取
    if filename:
        ext = Path(filename).suffix.lower().lstrip('.')
        if ext:
            logger.info(f"从文件名获取类型: {ext}")
            return ext
    
    # 无法确定类型
    logger.warning("无法检测文件类型")
    return ""

def create_temp_dir() -> Path:
    """
    创建临时目录
    
    Returns:
        Path: 临时目录路径
    """
    temp_dir = Path(tempfile.mkdtemp())
    logger.debug(f"创建临时目录: {temp_dir}")
    return temp_dir

def clean_temp_dir(temp_dir: Path) -> None:
    """
    清理临时目录
    
    Args:
        temp_dir: 临时目录路径
    """
    try:
        shutil.rmtree(temp_dir, ignore_errors=True)
        logger.debug(f"清理临时目录: {temp_dir}")
    except Exception as e:
        logger.warning(f"清理临时目录失败: {temp_dir}, 错误: {str(e)}")

def ensure_dir_exists(dir_path: Union[str, Path]) -> Path:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        dir_path: 目录路径
        
    Returns:
        Path: 目录路径对象
    """
    path = Path(dir_path)
    path.mkdir(parents=True, exist_ok=True)
    return path

def get_file_size(file_path: Union[str, Path]) -> int:
    """
    获取文件大小（字节）
    
    Args:
        file_path: 文件路径
        
    Returns:
        int: 文件大小（字节）
    """
    return os.path.getsize(file_path)

def is_valid_file(file_path: Union[str, Path]) -> bool:
    """
    检查文件是否有效（存在且不是目录）
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 文件是否有效
    """
    path = Path(file_path)
    return path.exists() and path.is_file()
