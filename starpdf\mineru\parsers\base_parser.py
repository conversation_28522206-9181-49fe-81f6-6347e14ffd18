from abc import ABC, abstractmethod
from pathlib import Path
import re
import os
import tempfile
from typing import Optional, Dict, Any, Tuple

from ..utils.logger import get_logger

# 🔹 抽象策略：定义解析器接口
class DocumentParser(ABC):
    """
    文档解析器抽象基类
    定义了所有文档解析器必须实现的接口
    """
    def __init__(self):
        self.model_manager = None
        self.logger = get_logger(self.__class__.__name__)

    @abstractmethod
    def parse(self,
              file_bytes: bytes,
              filename: str,
              output_dir: Path,
              opts: Dict[str, Any]) -> Optional[Path]:
        """
        解析文档并生成Markdown

        Args:
            file_bytes: 文档的字节内容
            filename: 原始文件名（带扩展名）
            output_dir: 输出目录
            opts: 附加选项

        Returns:
            Optional[Path]: 生成的Markdown文件路径，失败时返回None
        """
        pass

    def _sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除不安全字符

        Args:
            filename: 原始文件名

        Returns:
            str: 清理后的安全文件名
        """
        return re.sub(r'[^\w\-_\. ]', '_', filename)

    def set_model_manager(self, model_manager) -> None:
        """
        设置模型管理器

        Args:
            model_manager: 模型管理器实例
        """
        self.model_manager = model_manager

    def create_temp_file(self,
                         file_bytes: bytes,
                         suffix: str) -> Tuple[Path, tempfile.NamedTemporaryFile]:
        """
        创建临时文件

        Args:
            file_bytes: 文件字节内容
            suffix: 文件后缀（如 .docx）

        Returns:
            Tuple[Path, NamedTemporaryFile]: 临时文件路径和文件对象
        """
        tmp_file = tempfile.NamedTemporaryFile(
            mode='wb',
            suffix=suffix,
            delete=False
        )
        tmp_file.write(file_bytes)
        tmp_file.flush()
        return Path(tmp_file.name), tmp_file

    def save_original_file(self,
                          file_bytes: bytes,
                          filename: str,
                          output_dir: Path) -> Path:
        """
        保存原始文件到输出目录

        Args:
            file_bytes: 文件字节内容
            filename: 文件名
            output_dir: 输出目录

        Returns:
            Path: 保存的文件路径
        """
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 保存原始文件
        orig_file_path = output_dir / filename
        with open(orig_file_path, 'wb') as f:
            f.write(file_bytes)

        self.logger.info(f"已保存原始文件: {orig_file_path}")
        return orig_file_path