"""
上传并处理路由模块
包含专利上传并处理的API接口，支持单个专利和批量处理
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Form, UploadFile, File
from fastapi.responses import JSONResponse
import logging
import os
import time
import json
import shutil
import zipfile
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
import asyncio

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()
# 服务器核心实例，将在注册路由时设置
server_core = None

# 从process_routes导入响应模型和处理函数
from .process_routes import ProcessResponse, BatchProcessResponse, _process_patent_task

def find_patent_dirs(base_dir):
    """
    查找专利目录
    
    参数:
        base_dir: 基础目录
        
    返回:
        List[str]: 专利目录列表
    """
    if not os.path.exists(base_dir) or not os.path.isdir(base_dir):
        return []
    
    # 检查是否有子目录
    subdirs = [os.path.join(base_dir, d) for d in os.listdir(base_dir)
              if os.path.isdir(os.path.join(base_dir, d)) and not d.startswith('.')]
    
    if subdirs:
        return subdirs
    else:
        # 如果没有子目录，将基础目录作为专利目录
        return [base_dir]

def extract_archive(archive_path, extract_dir=None):
    """
    解压缩文件
    
    参数:
        archive_path: 压缩文件路径
        extract_dir: 解压目录
        
    返回:
        str: 解压目录
    """
    if not extract_dir:
        extract_dir = os.path.join(os.path.dirname(archive_path), f"extract_{int(time.time())}")
    
    os.makedirs(extract_dir, exist_ok=True)
    
    try:
        # 解压ZIP文件
        with zipfile.ZipFile(archive_path, 'r') as zip_ref:
            zip_ref.extractall(extract_dir)
        return extract_dir
    except Exception as e:
        logger.error(f"解压文件失败: {str(e)}")
        return None

@router.post("/upload_and_process_alt")
async def upload_and_process_alt(
    patent_folder: UploadFile = File(...),
    batch_mode: str = Form("false"),
    output_dir: Optional[str] = Form(None),
    options: Optional[str] = Form(None)
):
    """
    上传并处理专利目录（ZIP文件）
    
    参数:
        patent_folder: 上传的专利目录（ZIP文件）
        batch_mode: 是否使用批处理模式
        output_dir: 输出目录
        options: 处理选项（JSON字符串）
        
    返回:
        Dict: 处理结果
    """
    if not server_core:
        raise HTTPException(status_code=500, detail="服务器核心未初始化")
    
    try:
        # 创建临时目录用于存储上传的文件
        temp_dir = os.path.join(os.getcwd(), "uploads", f"temp_{int(time.time())}")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 保存上传的文件
        zip_path = os.path.join(temp_dir, patent_folder.filename)
        with open(zip_path, "wb") as f:
            contents = await patent_folder.read()
            f.write(contents)
        
        # 解析处理选项
        process_options = {}
        if options:
            try:
                if isinstance(options, str):
                    process_options = json.loads(options)
                elif isinstance(options, dict):
                    process_options = options
            except json.JSONDecodeError:
                logger.warning(f"无法解析处理选项: {options}")
        
        # 解压文件
        extract_dir = extract_archive(zip_path)
        if not extract_dir:
            raise HTTPException(status_code=500, detail="解压文件失败")
        
        # 检查是否使用批处理模式
        is_batch_mode = batch_mode.lower() == "true"
        logger.info(f"使用批处理模式: {is_batch_mode}")
        
        # 记录开始时间
        start_time = time.time()
        
        if is_batch_mode:
            # 批处理模式 - 查找所有专利子目录
            patent_dirs = find_patent_dirs(extract_dir)
            
            if not patent_dirs:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "error": "在解压后的目录中未找到专利子目录"}
                )
            
            # 处理所有专利
            tasks = []
            for patent_dir in patent_dirs:
                # 从目录名提取专利ID
                patent_id = os.path.basename(patent_dir)
                logger.info(f"启动专利任务: {patent_id}, 路径: {patent_dir}")
                
                # 创建任务选项
                task_options = process_options.copy()
                
                # 创建任务并立即启动
                task = asyncio.create_task(_process_patent_task(
                    patent_id=patent_id,
                    patent_path=patent_dir,
                    output_dir=output_dir,
                    options=task_options
                ))
                tasks.append(task)
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks)
            
            # 处理结果
            total = len(results)
            processed = 0
            failed = 0
            failed_patents = []
            
            for result in results:
                if result.get("success", False):
                    processed += 1
                else:
                    failed += 1
                    failed_patents.append({
                        "patent_id": result.get("patent_id", "未知"),
                        "message": result.get("message", "处理失败，未知原因")
                    })
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 获取输出目录路径
            results_path = output_dir if output_dir else extract_dir
            
            # 构造下载链接
            download_url = f"/api/download_batch_results?result_dir={results_path}"
            download_path = results_path
            
            # 构造返回结果
            return BatchProcessResponse(
                success=processed > 0,
                total=total,
                processed=processed,
                failed=failed,
                message=f"批量处理完成，成功: {processed}，失败: {failed}",
                results_path=results_path,
                download_url=download_url,
                download_path=download_path,
                processing_time=processing_time,
                failed_patents=failed_patents if failed > 0 else None
            )
        else:
            # 单个专利处理模式
            # 查找专利目录
            patent_dirs = find_patent_dirs(extract_dir)
            
            if not patent_dirs:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "error": "在解压后的目录中未找到专利目录"}
                )
            
            # 使用第一个目录作为专利目录
            patent_dir = patent_dirs[0]
            patent_id = os.path.basename(patent_dir)
            
            # 处理专利
            result = await _process_patent_task(
                patent_id=patent_id,
                patent_path=patent_dir,
                output_dir=output_dir,
                options=process_options
            )
            
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            return ProcessResponse(**result)
    
    except Exception as e:
        logger.error(f"上传并处理专利失败: {str(e)}")
        # 清理临时目录
        if 'temp_dir' in locals():
            shutil.rmtree(temp_dir, ignore_errors=True)
        raise HTTPException(status_code=500, detail=f"上传并处理专利失败: {str(e)}")

def setup_routes(app, _server_core):
    """
    设置路由
    
    参数:
        app: FastAPI应用
        _server_core: 服务器核心
    """
    global server_core
    server_core = _server_core
    app.include_router(router, tags=["upload_process"])
