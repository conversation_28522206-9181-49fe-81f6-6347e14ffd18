from typing import Dict, Type, Optional
from pathlib import Path

from base_parser import DocumentParser
from word_parser import OnlineWordParser
from excel_parser import OnlineExcelParser
from logger import get_logger
from config import config

class ParserFactory:
    """
    解析器工厂类
    负责创建和管理不同类型的文档解析器
    """
    
    # 解析器类型映射
    _parser_classes: Dict[str, Type[DocumentParser]] = {
        "doc": OnlineWordParser,
        "docx": OnlineWordParser,
        "xls": OnlineExcelParser,
        "xlsx": OnlineExcelParser,
    }
    
    # 解析器实例缓存
    _parser_instances: Dict[str, DocumentParser] = {}
    
    @classmethod
    def get_parser(cls, file_type: str) -> Optional[DocumentParser]:
        """
        获取指定类型的解析器实例
        
        Args:
            file_type: 文件类型（扩展名，不含点，如'docx'）
            
        Returns:
            DocumentParser: 解析器实例，如果不支持该类型则返回None
        """
        logger = get_logger("ParserFactory")
        
        # 转换为小写
        file_type = file_type.lower()
        
        # 检查是否支持该类型
        if file_type not in cls._parser_classes:
            logger.warning(f"不支持的文件类型: {file_type}")
            return None
        
        # 检查配置是否启用该解析器
        parser_enabled = config.get(f"parsers.{file_type}.enabled", True)
        if not parser_enabled:
            logger.warning(f"解析器已禁用: {file_type}")
            return None
        
        # 如果已有实例，直接返回
        if file_type in cls._parser_instances:
            return cls._parser_instances[file_type]
        
        # 创建新实例
        try:
            parser_class = cls._parser_classes[file_type]
            parser = parser_class()
            cls._parser_instances[file_type] = parser
            logger.info(f"创建解析器: {file_type} -> {parser.__class__.__name__}")
            return parser
        except Exception as e:
            logger.error(f"创建解析器失败: {file_type}, 错误: {str(e)}")
            return None
    
    @classmethod
    def get_parser_for_file(cls, filename: str) -> Optional[DocumentParser]:
        """
        根据文件名获取合适的解析器
        
        Args:
            filename: 文件名（带扩展名）
            
        Returns:
            DocumentParser: 解析器实例，如果不支持该类型则返回None
        """
        # 获取文件扩展名（不含点）
        file_ext = Path(filename).suffix.lower().lstrip('.')
        return cls.get_parser(file_ext)
    
    @classmethod
    def register_parser(cls, file_type: str, parser_class: Type[DocumentParser]) -> None:
        """
        注册新的解析器类型
        
        Args:
            file_type: 文件类型（扩展名，不含点，如'docx'）
            parser_class: 解析器类
        """
        logger = get_logger("ParserFactory")
        cls._parser_classes[file_type.lower()] = parser_class
        # 清除该类型的实例缓存，以便下次获取时创建新实例
        if file_type.lower() in cls._parser_instances:
            del cls._parser_instances[file_type.lower()]
        logger.info(f"注册解析器: {file_type} -> {parser_class.__name__}")
    
    @classmethod
    def get_supported_types(cls) -> list:
        """
        获取所有支持的文件类型
        
        Returns:
            list: 支持的文件类型列表
        """
        return list(cls._parser_classes.keys())
