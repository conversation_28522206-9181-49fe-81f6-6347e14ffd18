# MinerU 文档转换服务

MinerU是一个文档转换服务，能够将各种格式的文档（Word、Excel、PDF等）转换为Markdown格式。

## 功能特点

- 支持多种文档格式：Word (.doc, .docx)、Excel (.xls, .xlsx)、PDF、图片等
- 使用策略模式处理不同类型的文档
- 支持多GPU并行处理
- 提供RESTful API接口
- 配置灵活，支持命令行参数和配置文件

## 系统架构

系统采用分层架构设计：

1. **API层**：提供RESTful接口，处理HTTP请求
2. **服务层**：核心业务逻辑，协调各个组件
3. **解析器层**：使用策略模式处理不同类型的文档
4. **配置层**：管理系统配置
5. **工具层**：提供通用功能

### 文件结构

```
onlineStrategyPatternV3/
├── api/                    # API层
│   ├── __init__.py
│   └── routes.py           # API路由
├── core/                   # 核心服务层
│   ├── __init__.py
│   └── mineru_api.py       # 核心API实现
├── parsers/                # 解析器层
│   ├── __init__.py
│   ├── base_parser.py      # 基础解析器
│   ├── parser_factory.py   # 解析器工厂
│   ├── word_parser.py      # Word解析器
│   └── excel_parser.py     # Excel解析器
├── utils/                  # 工具层
│   ├── __init__.py
│   ├── config.py           # 配置管理
│   ├── logger.py           # 日志管理
│   ├── exceptions.py       # 异常处理
│   └── helpers.py          # 辅助函数
├── client/                 # 客户端
│   ├── __init__.py
│   ├── mineru_client_test.py # 客户端测试
│   └── client_example.py   # 客户端示例
├── config/                 # 配置文件
│   └── config.yaml.example # 配置示例
├── mineru_server.py        # 主入口
├── requirements.txt        # 依赖项
└── README.md               # 文档
```

## 安装与配置

### 依赖项

- Python 3.7+
- FastAPI
- PyTorch
- LitServe
- PyMuPDF (fitz)
- python-docx
- openpyxl
- filetype

### 安装步骤

1. 克隆代码库
2. 安装依赖：`pip install -r requirements.txt`
3. 配置环境变量或创建配置文件

## 使用方法

### 启动服务器

```bash
python mineru_server.py --port 8010 --host 0.0.0.0 --gpu-devices 0,1
```

### 命令行参数

- `--port`: 服务器端口（默认：8010）
- `--host`: 服务器主机（默认：0.0.0.0）
- `--output-dir`: 输出目录
- `--gpu-devices`: GPU设备ID，用逗号分隔
- `--workers-per-device`: 每个设备的工作进程数
- `--timeout`: 启用超时
- `--config`: 配置文件路径

### 配置文件

可以创建YAML格式的配置文件：

```yaml
server:
  host: 0.0.0.0
  port: 8010
  workers_per_device: 2
  timeout: false

output:
  base_dir: /path/to/output
  create_if_missing: true

gpu:
  devices: [0, 1]
  accelerator: cuda
```

## API接口

### 文档转换

**请求**：
```
POST /predict
```

**请求体**：
```json
{
  "file": "base64编码的文件内容",
  "kwargs": {
    "debug_able": false,
    "parse_method": "auto"
  },
  "request_id": "可选的请求ID"
}
```

**响应**：
```json
{
  "request_id": "请求ID",
  "output_dir": "输出目录路径"
}
```

### 获取文件

**请求**：
```
GET /files?path={文件路径}
```

**响应**：
文件内容

### 列出目录

**请求**：
```
GET /files/list?path={目录路径}
```

**响应**：
```json
{
  "path": "目录路径",
  "files": [
    {
      "name": "文件名",
      "isDirectory": false,
      "size": 1024,
      "lastModified": 1620000000,
      "path": "相对路径"
    }
  ],
  "total": 1,
  "directories": 0,
  "regularFiles": 1
}
```

### 健康检查

**请求**：
```
GET /ping
```

**响应**：
```json
{
  "status": "ok",
  "message": "MinerU 服务器正在运行"
}
```

## 开发指南

### 添加新的文档解析器

1. 继承`DocumentParser`基类
2. 实现`parse`方法
3. 在`parser_factory.py`中注册新的解析器

```python
from parsers.base_parser import DocumentParser

class MyNewParser(DocumentParser):
    def parse(self, file_bytes, filename, output_dir, opts):
        # 实现解析逻辑
        pass

# 注册解析器
from parsers.parser_factory import ParserFactory
ParserFactory.register_parser("新格式扩展名", MyNewParser)
```

## 客户端使用示例

```python
from client.client_example import convert_document, download_markdown

# 转换文档
result = convert_document(
    file_path="document.docx",
    server_url="http://127.0.0.1:8010"
)

# 下载Markdown文件
md_path = download_markdown(
    result=result,
    server_url="http://127.0.0.1:8010",
    output_path="output.md"
)
```

## 故障排除

### 常见问题

1. **无法启动服务器**
   - 检查端口是否被占用
   - 确保GPU设备可用

2. **文件转换失败**
   - 检查文件格式是否支持
   - 查看日志获取详细错误信息

3. **GPU内存不足**
   - 减少每个设备的工作进程数
   - 使用更多GPU设备分担负载

## 许可证

本项目采用MIT许可证。
