import os
from pathlib import Path
from typing import Dict, Any, List, Optional
import logging
import yaml

# 默认配置
DEFAULT_CONFIG = {
    # 服务配置
    "server": {
        "host": "0.0.0.0",
        "port": 8010,
        "workers_per_device": 2,
        "timeout": False,
    },

    # 输出目录配置
    "output": {
        "base_dir": "/home/<USER>/zhouxingyu/zxy_extractor/data/tmp/mineru",
        "create_if_missing": True,
    },

    # 日志配置
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "file": "mineru_server.log",
        "console": True,
    },

    # 解析器配置
    "parsers": {
        "word": {
            "enabled": True,
            "convert_tool": "soffice",
        },
        "excel": {
            "enabled": True,
            "convert_tool": "soffice",
        },
        "pdf": {
            "enabled": True,
        },
    },

    # GPU配置
    "gpu": {
        "devices": [0, 1],
        "accelerator": "cuda",
    },

    # 临时文件配置
    "temp": {
        "cleanup": True,
    },

    # 处理选项配置
    "processing_options": {
        "f_dump_middle_json": False,
        "f_dump_model_json": False,
        "f_dump_orig_pdf": True,
        "f_dump_content_list": False,
        "f_draw_model_bbox": False,
        "f_draw_layout_bbox": False,
        "f_draw_span_bbox": False,
        "f_draw_line_sort_bbox": False,
        "f_draw_char_bbox": False,
    }
}

class Config:
    """配置管理类，负责加载和提供配置信息"""

    _instance = None

    def __new__(cls, config_path: Optional[str] = None):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, config_path: Optional[str] = None):
        """初始化配置管理器"""
        if self._initialized:
            return

        self.logger = logging.getLogger("config")
        self.config_data = DEFAULT_CONFIG.copy()

        # 如果提供了配置文件路径，尝试加载
        if config_path and os.path.exists(config_path):
            self._load_from_file(config_path)

        # 从环境变量加载配置
        self._load_from_env()

        # 确保输出目录存在
        if self.config_data["output"]["create_if_missing"]:
            output_dir = Path(self.get("output.base_dir"))
            if not output_dir.exists():
                output_dir.mkdir(parents=True, exist_ok=True)
                self.logger.info(f"创建输出目录: {output_dir}")

        self._initialized = True
        self.logger.debug("配置初始化完成")

    def _load_from_file(self, config_path: str) -> None:
        """从YAML文件加载配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                file_config = yaml.safe_load(f)
                if file_config:
                    # 递归更新配置
                    self._update_dict(self.config_data, file_config)
                    self.logger.info(f"从文件加载配置: {config_path}")
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")

    def _load_from_env(self) -> None:
        """从环境变量加载配置"""
        # 输出目录
        if "MINERU_OUTPUT_DIR" in os.environ:
            self.config_data["output"]["base_dir"] = os.environ["MINERU_OUTPUT_DIR"]
            self.logger.info(f"从环境变量加载输出目录: {self.config_data['output']['base_dir']}")

        # 服务器端口
        if "MINERU_PORT" in os.environ:
            try:
                self.config_data["server"]["port"] = int(os.environ["MINERU_PORT"])
                self.logger.info(f"从环境变量加载端口: {self.config_data['server']['port']}")
            except ValueError:
                self.logger.warning(f"无效的端口环境变量: {os.environ['MINERU_PORT']}")

        # GPU设备
        if "CUDA_VISIBLE_DEVICES" in os.environ:
            try:
                devices = [int(d) for d in os.environ["CUDA_VISIBLE_DEVICES"].split(",")]
                self.config_data["gpu"]["devices"] = devices
                self.logger.info(f"从环境变量加载GPU设备: {devices}")
            except ValueError:
                self.logger.warning(f"无效的GPU设备环境变量: {os.environ['CUDA_VISIBLE_DEVICES']}")

    def _update_dict(self, target: Dict, source: Dict) -> None:
        """递归更新字典"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._update_dict(target[key], value)
            else:
                target[key] = value

    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值

        Args:
            key_path: 点分隔的配置路径，如 "server.port"
            default: 如果配置不存在，返回的默认值

        Returns:
            配置值
        """
        parts = key_path.split('.')
        value = self.config_data

        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return default

        return value

    def set(self, key_path: str, value: Any) -> None:
        """
        设置配置值

        Args:
            key_path: 点分隔的配置路径，如 "server.port"
            value: 要设置的值
        """
        parts = key_path.split('.')
        target = self.config_data

        # 导航到最后一级的父级
        for part in parts[:-1]:
            if part not in target:
                target[part] = {}
            target = target[part]

        # 设置值
        target[parts[-1]] = value

    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config_data.copy()

# 创建全局配置实例
config = Config()
