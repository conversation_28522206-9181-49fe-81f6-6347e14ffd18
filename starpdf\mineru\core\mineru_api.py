import os
import uuid
import fitz
import torch
import base64
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, Union, List

import litserve as ls

from ..utils.config import config
from ..utils.logger import get_logger
from ..utils.helpers import clean_memory, decode_base64, detect_file_type
from ..parsers.parser_factory import ParserFactory
from ..utils.exceptions import (
    FileTypeNotSupportedError,
    FileProcessingError,
    ParserNotFoundError
)

logger = get_logger("mineru_api")

class MinerUAPI(ls.LitAPI):
    """
    MinerU API 类
    处理文档转换为Markdown的核心逻辑
    """

    def __init__(self, output_dir: Optional[str] = None):
        """
        初始化MinerU API

        Args:
            output_dir: 输出目录，如果为None则使用配置中的值
        """
        # 获取输出目录
        if output_dir is None:
            output_dir = config.get("output.base_dir")

        # 确保使用绝对路径
        self.output_dir = Path(os.path.abspath(output_dir))
        if not self.output_dir.exists():
            self.output_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"初始化 MinerUAPI，输出目录: {self.output_dir}")

    def setup(self, device: str) -> None:
        """
        设置设备和加载模型

        Args:
            device: 设备标识符（如'cuda:0'）
        """
        if device.startswith('cuda'):
            os.environ['CUDA_VISIBLE_DEVICES'] = device.split(':')[-1]
            if torch.cuda.device_count() > 1:
                raise RuntimeError("Remove any CUDA actions before setting 'CUDA_VISIBLE_DEVICES'.")

        try:
            from magic_pdf.tools.cli import do_parse, convert_file_to_pdf
            from magic_pdf.model.doc_analyze_by_custom_model import ModelSingleton

            self.do_parse = do_parse
            self.convert_file_to_pdf = convert_file_to_pdf

            model_manager = ModelSingleton()
            model_manager.get_model(True, False)
            model_manager.get_model(False, False)
            logger.info(f'模型初始化完成，设备: {device}')
        except ImportError as e:
            logger.error(f"导入 magic_pdf 模块错误: {str(e)}")

    def decode_request(self, request: Dict[str, Any]) -> Tuple[str, Optional[bytes], Dict[str, Any], bool]:
        """
        解码请求数据

        Args:
            request: 客户端请求数据

        Returns:
            tuple: (request_id, file_bytes, opts, is_direct_markdown)
                - request_id: 请求ID
                - file_bytes: 文件字节内容（可能为None，如果是直接处理为Markdown的文件）
                - opts: 处理选项
                - is_direct_markdown: 是否直接处理为Markdown（不需要PDF转换）
        """
        file_base64 = request.get('file')
        if not file_base64:
            raise ValueError("请求中缺少'file'字段")

        request_id = request.get('request_id', str(uuid.uuid4()))
        logger.info(f"收到请求: {request_id}")

        # 获取处理选项
        opts = request.get('kwargs', {})
        opts.setdefault('debug_able', False)
        opts.setdefault('parse_method', 'auto')

        # 从配置文件中读取处理选项的默认值
        opts.setdefault('f_dump_middle_json', config.get('processing_options.f_dump_middle_json', False))
        opts.setdefault('f_dump_model_json', config.get('processing_options.f_dump_model_json', False))
        opts.setdefault('f_dump_orig_pdf', config.get('processing_options.f_dump_orig_pdf', True))
        opts.setdefault('f_dump_content_list', config.get('processing_options.f_dump_content_list', False))
        opts.setdefault('f_draw_model_bbox', config.get('processing_options.f_draw_model_bbox', False))
        opts.setdefault('f_draw_layout_bbox', config.get('processing_options.f_draw_layout_bbox', False))
        opts.setdefault('f_draw_span_bbox', config.get('processing_options.f_draw_span_bbox', False))
        opts.setdefault('f_draw_line_sort_bbox', config.get('processing_options.f_draw_line_sort_bbox', False))
        opts.setdefault('f_draw_char_bbox', config.get('processing_options.f_draw_char_bbox', False))

        # 添加请求ID到选项中，供解析器使用
        opts['request_id'] = request_id
        logger.info(f"请求 {request_id} 的处理选项: {opts}")

        # 转换文件为PDF或直接处理为Markdown
        file_bytes = self.cvt2pdf(file_base64, request_id)

        # 判断是否直接处理为Markdown（file_bytes为None表示已直接处理）
        is_direct_markdown = file_bytes is None

        return request_id, file_bytes, opts, is_direct_markdown

    def predict(self, inputs: Tuple) -> Dict[str, Any]:
        """
        处理请求

        Args:
            inputs: 解码后的请求数据

        Returns:
            dict: 处理结果
        """
        # 解包输入
        if len(inputs) == 4:
            request_id, file_bytes, opts, is_direct_markdown = inputs
        else:
            # 兼容旧版本接口
            request_id, file_bytes, opts = inputs
            is_direct_markdown = False

        output_dir = self.output_dir.joinpath(request_id)

        try:
            logger.info(f"开始处理请求: {request_id}")

            # 如果是直接处理为Markdown的文件类型（如Word、Excel），跳过PDF处理
            if is_direct_markdown:
                logger.info(f"文件已直接处理为Markdown: {request_id}")
            else:
                # 使用PDF处理流程
                if file_bytes:
                    # 创建一个不包含request_id的选项副本
                    parse_opts = opts.copy()
                    if 'request_id' in parse_opts:
                        del parse_opts['request_id']

                    # 使用不包含request_id的选项调用do_parse
                    self.do_parse(self.output_dir, request_id, file_bytes, [], **parse_opts)
                else:
                    logger.error(f"文件内容为空: {request_id}")
                    raise FileProcessingError("文件内容为空", request_id=request_id)

            # 检查输出目录
            auto_dir = output_dir / 'auto'
            if auto_dir.exists():
                logger.info(f"输出目录存在: {auto_dir}")

                # 检查Markdown文件是否存在
                md_file = auto_dir / f"{request_id}.md"
                if md_file.exists():
                    logger.info(f"Markdown文件存在: {md_file}")
                else:
                    logger.warning(f"Markdown文件不存在: {md_file}")
                    # 列出目录中的所有文件
                    logger.info(f"目录内容: {list(auto_dir.glob('*'))}")
            else:
                logger.warning(f"输出目录不存在: {auto_dir}")

            logger.info(f"请求处理完成: {request_id}, 输出目录: {output_dir}")
            return {'request_id': request_id, 'output_dir': str(output_dir)}
        except Exception as e:
            logger.error(f"请求处理错误: {request_id}, 错误: {str(e)}")
            # 不要删除输出目录，以便于调试
            # shutil.rmtree(output_dir, ignore_errors=True)
            raise FileProcessingError(str(e), request_id=request_id)
        finally:
            self.clean_memory()

    def encode_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """
        编码响应数据

        Args:
            response: 处理结果

        Returns:
            dict: 编码后的响应数据
        """
        return response

    def clean_memory(self) -> None:
        """清理内存和GPU缓存"""
        clean_memory()

    def cvt2pdf(self, file_base64: str, request_id: Optional[str] = None) -> Optional[bytes]:
        """
        将文件转换为PDF或处理为Markdown

        Args:
            file_base64: Base64编码的文件内容
            request_id: 请求ID，用于创建输出目录

        Returns:
            bytes: PDF文件内容或None（如果是直接处理为Markdown的文件类型）
        """
        # 解码文件内容
        try:
            file_bytes = decode_base64(file_base64)
        except ValueError as e:
            logger.error(f"Base64解码失败: {str(e)}")
            raise FileProcessingError(f"Base64解码失败: {str(e)}", request_id=request_id)

        # 检测文件类型
        file_ext = detect_file_type(file_bytes)
        if not file_ext:
            raise FileTypeNotSupportedError("未知", ParserFactory.get_supported_types())

        logger.info(f"检测到文件类型: {file_ext}")

        # 根据文件类型进行处理
        if file_ext == 'pdf':
            # PDF文件直接返回
            return file_bytes

        elif file_ext in ['jpg', 'png', 'jpeg', 'gif']:
            # 图片文件转换为PDF
            try:
                with fitz.open(stream=file_bytes, filetype=file_ext) as f:
                    return f.convert_to_pdf()
            except Exception as e:
                logger.error(f"图片转换为PDF失败: {str(e)}")
                raise FileProcessingError(f"图片转换为PDF失败: {str(e)}", request_id=request_id)

        elif file_ext in ['doc', 'docx']:
            # Word文档处理
            if request_id:
                # 使用自定义解析器直接处理为Markdown
                parser = ParserFactory.get_parser(file_ext)
                if not parser:
                    raise ParserNotFoundError(file_ext)

                output_dir = self.output_dir
                filename = f"document.{file_ext}"
                opts = {'request_id': request_id}

                # 调用Word解析器
                parser.parse(file_bytes, filename, output_dir, opts)
                return None  # 不返回PDF内容，因为已经直接处理为Markdown
            else:
                # 如果没有请求ID，使用传统方式转换为PDF
                from tempfile import NamedTemporaryFile
                with NamedTemporaryFile(suffix=f".{file_ext}", delete=False) as temp_file:
                    temp_path = Path(temp_file.name)
                    temp_file.write(file_bytes)

                try:
                    self.convert_file_to_pdf(temp_path, temp_path.parent)
                    pdf_path = temp_path.with_suffix('.pdf')
                    pdf_bytes = pdf_path.read_bytes()

                    # 清理临时文件
                    temp_path.unlink(missing_ok=True)
                    pdf_path.unlink(missing_ok=True)

                    return pdf_bytes
                except Exception as e:
                    # 清理临时文件
                    temp_path.unlink(missing_ok=True)
                    logger.error(f"Word转换为PDF失败: {str(e)}")
                    raise FileProcessingError(f"Word转换为PDF失败: {str(e)}", request_id=request_id)

        elif file_ext in ['xls', 'xlsx']:
            # Excel文档处理
            if request_id:
                # 使用自定义解析器直接处理为Markdown
                parser = ParserFactory.get_parser(file_ext)
                if not parser:
                    raise ParserNotFoundError(file_ext)

                output_dir = self.output_dir
                filename = f"spreadsheet.{file_ext}"
                opts = {'request_id': request_id}

                # 调用Excel解析器
                parser.parse(file_bytes, filename, output_dir, opts)
                return None  # 不返回PDF内容，因为已经直接处理为Markdown
            else:
                # 如果没有请求ID，尝试转换为PDF（可能不完美）
                from tempfile import NamedTemporaryFile
                with NamedTemporaryFile(suffix=f".{file_ext}", delete=False) as temp_file:
                    temp_path = Path(temp_file.name)
                    temp_file.write(file_bytes)

                try:
                    self.convert_file_to_pdf(temp_path, temp_path.parent)
                    pdf_path = temp_path.with_suffix('.pdf')
                    pdf_bytes = pdf_path.read_bytes()

                    # 清理临时文件
                    temp_path.unlink(missing_ok=True)
                    pdf_path.unlink(missing_ok=True)

                    return pdf_bytes
                except Exception as e:
                    # 清理临时文件
                    temp_path.unlink(missing_ok=True)
                    logger.error(f"Excel转换为PDF失败: {str(e)}")
                    raise FileProcessingError(f"Excel转换为PDF失败: {str(e)}", request_id=request_id)

        elif file_ext in ['ppt', 'pptx']:
            # PowerPoint文档转换为PDF
            from tempfile import NamedTemporaryFile
            with NamedTemporaryFile(suffix=f".{file_ext}", delete=False) as temp_file:
                temp_path = Path(temp_file.name)
                temp_file.write(file_bytes)

            try:
                self.convert_file_to_pdf(temp_path, temp_path.parent)
                pdf_path = temp_path.with_suffix('.pdf')
                pdf_bytes = pdf_path.read_bytes()

                # 清理临时文件
                temp_path.unlink(missing_ok=True)
                pdf_path.unlink(missing_ok=True)

                return pdf_bytes
            except Exception as e:
                # 清理临时文件
                temp_path.unlink(missing_ok=True)
                logger.error(f"PowerPoint转换为PDF失败: {str(e)}")
                raise FileProcessingError(f"PowerPoint转换为PDF失败: {str(e)}", request_id=request_id)

        else:
            # 不支持的文件格式
            raise FileTypeNotSupportedError(file_ext, ParserFactory.get_supported_types())
