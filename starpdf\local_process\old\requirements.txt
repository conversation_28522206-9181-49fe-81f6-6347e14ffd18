# 基础依赖
torch>=1.10.0
Pillow>=9.0.0
numpy>=1.19.0
opencv-python>=4.5.0
tqdm>=4.62.0
pathlib>=1.0.1
huggingface-hub>=0.4.0

# API服务端依赖
flask>=2.0.0
waitress>=2.0.0  # 用于生产环境部署
gunicorn>=20.1.0  # 用于生产环境部署（可选，在Linux上）

# 客户端依赖
requests>=2.27.0

# OCR依赖
paddleocr>=2.6.0  # 可选

# 数据处理和可视化依赖
openpyxl>=3.0.9
matplotlib>=3.5.0  # 如果需要可视化图表 

# 压缩文件处理依赖
rarfile>=4.0  # 处理RAR文件（需要系统安装unrar工具）
py7zr>=0.20.0  # 处理7z文件 