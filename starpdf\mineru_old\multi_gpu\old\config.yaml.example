# MinerU服务器配置示例
# 复制此文件为config.yaml并根据需要修改

# 服务器配置
server:
  # 服务器主机地址
  host: 0.0.0.0
  # 服务器端口
  port: 8010
  # 每个设备的工作进程数
  workers_per_device: 2
  # 是否启用超时
  timeout: false

# 输出目录配置
output:
  # 基础输出目录
  base_dir: /home/<USER>/zhouxingyu/zxy_extractor/data/tmp/mineru
  # 如果目录不存在是否创建
  create_if_missing: true

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: INFO
  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  # 日志文件
  file: mineru_server.log
  # 是否输出到控制台
  console: true

# 解析器配置
parsers:
  # Word文档解析器
  word:
    # 是否启用
    enabled: true
    # 转换工具
    convert_tool: soffice
  # Excel文档解析器
  excel:
    # 是否启用
    enabled: true
    # 转换工具
    convert_tool: soffice
  # PDF文档解析器
  pdf:
    # 是否启用
    enabled: true

# GPU配置
gpu:
  # GPU设备ID列表
  devices: [0, 1]
  # 加速器类型
  accelerator: cuda

# 临时文件配置
temp:
  # 是否清理临时文件
  cleanup: true
