import os
import time
import json
import gc
from tqdm import tqdm
import torch
import multiprocessing
from functools import partial
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple

from api.utils.result_manager import ResultManager
from api.processors.molcoref_processor import MolCorefProcessor

# Global variable to store processors per process
# This will allow each process to reuse its processor instance
_PROCESS_PROCESSORS = {}

class PatentProcessor:
    """处理单个专利目录"""
    
    def __init__(self, 
                 patent_dir: Union[str, Path],
                 result_manager: ResultManager,
                 device: str = "cpu",
                 processor: MolCorefProcessor = None,
                 preprocessed: bool = False):
        """
        初始化专利处理器
        
        参数:
            patent_dir: 专利目录的路径
            result_manager: 结果管理器实例
            device: 计算设备
            processor: 如果提供，使用已存在的MolCorefProcessor
            preprocessed: 是否预处理图像
        """
        self.patent_dir = Path(patent_dir)
        self.result_manager = result_manager
        self.device = device
        # 是否预处理
        self.preprocessed = preprocessed
        
        # 支持传入已加载的处理器
        self.processor = processor or MolCorefProcessor(
            result_manager=result_manager,
            device=device
        )
        
        # 读取图像列表文件
        self.reaction_images = self._read_image_list("reaction_img.txt")
        self.formula_images = self._read_image_list("formula_img.txt")
    
    def _read_image_list(self, filename: str) -> set:
        """
        读取图像列表文件
        
        参数:
            filename: 文件名
            
        返回:
            图像ID集合
        """
        try:
            file_path = self.patent_dir / filename
            if file_path.exists():
                with open(file_path, 'r') as f:
                    return {line.strip() for line in f if line.strip()}
            return set()
        except Exception as e:
            print(f"读取{filename}失败：{str(e)}")
            return set()
    
    def process(self, progress_callback=None) -> bool:
        """
        处理专利目录中的所有图像
        
        参数:
            progress_callback: 进度回调函数，接收(progress, message)两个参数，progress为0-1之间的浮点数
        
        返回:
            处理成功与否的布尔值
        """
        try:
            # 获取专利目录
            patent_dir = Path(self.patent_dir)
            print(f"处理专利: {patent_dir.name}")
            
            # 进度更新
            if progress_callback:
                progress_callback(0.02, f"开始处理专利 {patent_dir.name}")
            
            # 检查目录是否存在
            if not patent_dir.exists():
                print(f"专利目录不存在: {patent_dir}")
                if progress_callback:
                    progress_callback(0, f"错误: 专利目录不存在 {patent_dir}")
                return False
            
            # 获取图像目录
            image_dir = patent_dir / "image"
            
            # 检查图像目录是否存在
            if not self._validate_image_dir(image_dir):
                print(f"图像目录不存在或为空: {image_dir}")
                
                # 尝试查找位于子目录的图像目录
                potential_dirs = list(patent_dir.glob("**/image"))
                if potential_dirs:
                    print(f"在子目录中找到潜在的图像目录: {potential_dirs[0]}")
                    image_dir = potential_dirs[0]
                    if not self._validate_image_dir(image_dir):
                        if progress_callback:
                            progress_callback(0, f"错误: 未找到有效的图像目录")
                        return False
                else:
                    if progress_callback:
                        progress_callback(0, f"错误: 未找到图像目录")
                    return False
            
            # 获取图像文件列表
            if progress_callback:
                progress_callback(0.05, "扫描图像文件")
            
            image_files = self._find_images(image_dir)
            
            # 检查是否有图像
            if not image_files:
                print(f"未找到图像文件: {image_dir}")
                if progress_callback:
                    progress_callback(0, f"错误: 未找到图像文件")
                return False
            
            print(f"找到 {len(image_files)} 个图像文件")
            if progress_callback:
                progress_callback(0.1, f"找到 {len(image_files)} 个图像文件")
            
            # 处理每个图像
            success_count = 0
            total_images = len(image_files)
            
            # 在已处理列表中的图像ID
            processed_set = self._read_image_list(patent_dir / "processed_images.txt")
            
            # 已检查的图像ID
            checked_set = set()
            
            # 按数字顺序排序图像文件
            sorted_image_files = sorted(image_files, key=lambda f: int(Path(f).stem) if Path(f).stem.isdigit() else float('inf'))
            
            # 处理每个图像
            for i, image_file in enumerate(sorted_image_files):
                image_id = Path(image_file).stem
                
                # 计算进度并更新
                current_progress = 0.1 + (i / total_images) * 0.8  # 10%-90%的总进度给图像处理
                if progress_callback:
                    progress_callback(current_progress, f"处理图像 {i+1}/{total_images}: {image_id}")
                
                # 跳过已处理的图像（如果不是预处理模式）
                if not self.preprocessed and image_id in processed_set:
                    print(f"跳过已处理的图像: {image_id}")
                    checked_set.add(image_id)
                    success_count += 1
                    continue
                
                # 处理图像
                try:
                    # 使用MolCorefProcessor进行处理
                    if self.preprocessed:
                        # 预处理模式
                        if self.processor.molscribe_processor.process_preprocessed_image(image_file):
                            success_count += 1
                            checked_set.add(image_id)
                    else:
                        # 正常处理
                        if self.processor.process_image(image_file):
                            success_count += 1
                            checked_set.add(image_id)
                except Exception as e:
                    print(f"处理图像 {image_id} 时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
            
            # 写入已处理图像列表
            with open(patent_dir / "processed_images.txt", "w") as f:
                for image_id in checked_set:
                    f.write(f"{image_id}\n")
            
            # 最终清理和总结
            if progress_callback:
                progress_callback(0.95, "完成图像处理，保存结果")
            
            self.clear_cache()
            
            success_ratio = success_count / total_images if total_images > 0 else 0
            print(f"专利处理完成，成功率: {success_ratio:.2%} ({success_count}/{total_images})")
            
            if progress_callback:
                progress_callback(1.0, f"专利处理完成，成功率: {success_ratio:.2%} ({success_count}/{total_images})")
            
            # 至少有50%的图像处理成功，我们认为整个专利处理成功
            return success_ratio >= 0.5 and success_count > 0
            
        except Exception as e:
            print(f"处理专利时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            
            if progress_callback:
                progress_callback(0, f"处理专利失败: {str(e)}")
            
            return False
    
    def _validate_image_dir(self, image_dir: Path) -> bool:
        """验证图像目录"""
        if not image_dir.exists():
            print(f"跳过缺失的图像目录：{image_dir}")
            return False
        return True
    
    def _find_images(self, directory: Path):
        """查找支持的图像文件"""
        return sorted([p for ext in MolCorefProcessor.SUPPORTED_EXTENSIONS 
                      for p in directory.glob(f"*{ext.lower()}")])
    
    def write_to_excel(self, output_file: Path):
        """
        将结果写入Excel
        
        参数:
            output_file: Excel文件路径
        """
        # 获取所有分子和反应数据
        molecules_data = []
        for image_id in self.result_manager.molecule_results:
            molecules_data.extend(self.result_manager.get_molecule_results(image_id))
            
        reactions_data = []
        for image_id in self.result_manager.reaction_results:
            reactions_data.extend(self.result_manager.get_reaction_results(image_id))
        
        # 确保输出文件位于专利目录中（与processed_images.txt相同目录）
        patent_dir = Path(self.patent_dir)
        if not output_file.parent.samefile(patent_dir):
            output_file = patent_dir / output_file.name
            print(f"调整Excel输出路径为: {output_file}")
        
        # 调用处理器的Excel写入方法
        self.processor.write_to_excel(output_file, molecules_data, reactions_data)
        
        return output_file
        
    def clear_cache(self):
        """
        清理处理过程中产生的缓存，释放内存
        但保留核心处理器以便复用
        """
        # 保留processor引用以便复用
        processor = self.processor
        
        # 清理中间变量
        self.reaction_images = set()
        self.formula_images = set()
        
        # 强制执行垃圾回收
        gc.collect()
        
        # 如果启用了CUDA，也清理CUDA缓存
        if hasattr(torch, 'cuda') and torch.cuda.is_available():
            torch.cuda.empty_cache()
            
        # 恢复processor引用
        self.processor = processor
        
        return self


class FolderProcessor:
    """并行处理多个专利"""
    
    def __init__(self, 
                 input_root: str,
                 output_root: str = None,
                 devices: list = ["cpu"], 
                 output_config: Dict[str, bool] = None,
                 processes_per_gpu: int = 2):
        """
        初始化文件夹处理器
        
        参数:
            input_root: 包含专利文件夹的根目录
            output_root: 输出的根目录
            devices: 要使用的设备列表
            output_config: 输出生成的配置
            processes_per_gpu: 每个GPU的进程数
        """
        self.input_root = Path(input_root).resolve()
        self.output_root = Path(output_root) if output_root else self.input_root.parent / "molcoref_output"
        self.devices = devices
        self.output_config = output_config or {
            "json_results": True,
            "excel_results": True, 
            "visualization": True,
            "intermediate_files": False
        }
        self.processes_per_gpu = processes_per_gpu
        
        if not self.input_root.exists():
            raise FileNotFoundError(f"输入目录不存在：{self.input_root}")
        
        self.output_root.mkdir(parents=True, exist_ok=True)
        
        # 存储为每个进程创建的处理器
        self.processors = {}
    
    def process(self):
        """使用进程池处理所有专利"""
        # 查找所有专利目录
        patent_dirs = [d for d in self.input_root.iterdir() 
                      if d.is_dir() and not d.name.startswith(".")]
        
        if not patent_dirs:
            print(f"在{self.input_root}中未找到专利目录")
            return
        
        # 创建进程池
        num_processes = len(self.devices) * self.processes_per_gpu
        
        # 为进程分配设备
        device_assignments = []
        process_ids = []
        
        # 为每个专利分配一个设备和进程ID
        for i in range(len(patent_dirs)):
            # 决定使用哪个设备
            device_idx = i % len(self.devices)
            device = self.devices[device_idx]
            
            # 决定进程ID (每个设备有多个进程)
            process_id = (i // len(self.devices)) % self.processes_per_gpu
            
            device_assignments.append(device)
            process_ids.append(process_id)
        
        # 创建多进程上下文
        ctx = multiprocessing.get_context('spawn')
        pool = ctx.Pool(processes=num_processes)
        
        # 创建任务参数
        task = partial(process_patent_wrapper, 
                      output_root=self.output_root,
                      output_config=self.output_config,
                      model_paths=None)
        
        # 使用imap_unordered进行进度跟踪
        with tqdm(total=len(patent_dirs), desc="总进度") as pbar:
            for _ in pool.imap_unordered(task, zip(patent_dirs, device_assignments, process_ids)):
                pbar.update()
        
        pool.close()
        pool.join()
        
        # 清理全局处理器缓存
        global _PROCESS_PROCESSORS
        for key in list(_PROCESS_PROCESSORS.keys()):
            if _PROCESS_PROCESSORS[key] is not None:
                # 清理处理器中的模型
                processor = _PROCESS_PROCESSORS[key]
                if hasattr(processor, 'model'):
                    del processor.model
                if hasattr(processor, 'molscribe'):
                    del processor.molscribe
                if hasattr(processor, 'rxnmodel'):
                    del processor.rxnmodel
                del _PROCESS_PROCESSORS[key]
        _PROCESS_PROCESSORS.clear()
        
        # 强制垃圾回收
        gc.collect()
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print("已清理全局GPU缓存")
        
        print("已清理全局处理器缓存")

    def process_single_patent(self, patent_dir: Union[str, Path]):
        """
        处理单个专利（单进程使用）
        
        参数:
            patent_dir: 专利目录路径
        
        返回:
            处理成功返回True，否则返回False
        """
        patent_dir = Path(patent_dir)
        
        # 创建结果管理器
        result_manager = ResultManager(output_config=self.output_config)
        
        # 使用默认设备
        device = self.devices[0] if self.devices else "cpu"
        
        # 创建处理器（如果不存在）
        processor_key = f"proc_{device}"
        if processor_key not in self.processors:
            self.processors[processor_key] = MolCorefProcessor(
                result_manager=result_manager,
                device=device
            )
        
        # 创建并运行处理器
        processor = PatentProcessor(
            patent_dir=patent_dir,
            result_manager=result_manager,
            device=device,
            processor=self.processors[processor_key]  # 使用已加载的模型
        )
        success = processor.process()
        
        # 如果处理成功，保存结果
        if success:
            # 创建特定于专利的输出目录
            patent_output_dir = Path(self.output_root) / f"{patent_dir.name}"
            patent_output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存所有结果
            result_manager.save_results(patent_output_dir)
            
            # 如果配置了，保存Excel
            if self.output_config.get("excel_results", True):
                excel_file = patent_output_dir / f"{patent_dir.name}_chemicals.xlsx"
                processor.write_to_excel(excel_file)
        
        return success


def get_or_create_processor(device, process_id, output_config, model_paths=None):
    """
    获取或创建进程特定的处理器（确保每个进程只加载一次模型）
    
    参数:
        device: 计算设备
        process_id: 进程ID
        output_config: 输出配置
        model_paths: 模型路径
    
    返回:
        处理器实例
    """
    global _PROCESS_PROCESSORS
    
    # 创建唯一的处理器键
    processor_key = f"{device}_{process_id}"
    
    # 如果处理器已存在，直接返回（但不使用其result_manager）
    if processor_key in _PROCESS_PROCESSORS:
        return _PROCESS_PROCESSORS[processor_key]
    
    # 创建临时结果管理器，只用于初始化
    temp_result_manager = ResultManager(output_config=output_config)
    
    # 创建并保存处理器
    processor = MolCorefProcessor(
        result_manager=temp_result_manager,
        device=device,
        model_paths=model_paths
    )
    
    # 存储处理器以供重用
    _PROCESS_PROCESSORS[processor_key] = processor
    
    print(f"为进程 {os.getpid()} 创建了新的处理器实例 ({device}, {process_id})")
    return processor


def process_patent_wrapper(args, output_root, output_config, model_paths=None):
    """用于多进程的包装函数"""
    try:
        patent_dir, device, process_id = args
        
        # 处理设备名称
        process_device = device
        if device.startswith('cuda'):
            gpu_id = device.split(':')[1]
            
            # 查看当前环境中的CUDA设备
            if torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                current_cuda_devices = os.environ.get("CUDA_VISIBLE_DEVICES", "")
                
                print(f"进程 {os.getpid()} 处理专利时使用设备 {device}，当前环境变量 CUDA_VISIBLE_DEVICES={current_cuda_devices}，可见设备数量: {device_count}")
                
                # 找出请求的设备在可见设备列表中的位置
                if current_cuda_devices:
                    visible_devices = current_cuda_devices.split(',')
                    if gpu_id in visible_devices:
                        # 获取在当前可见列表中的索引
                        relative_id = visible_devices.index(gpu_id)
                        process_device = f"cuda:{relative_id}"
                    else:
                        # 如果请求的设备不在可见列表中，使用第一个可见设备
                        process_device = "cuda:0"
                        print(f"进程 {os.getpid()} 请求的设备 cuda:{gpu_id} 不在可见列表中，使用默认设备 {process_device}")
                else:
                    # 如果没有限制可见设备，则使用设备ID本身
                    process_device = f"cuda:0"  # 映射到当前进程的第一个设备
            else:
                print(f"进程 {os.getpid()} 没有可用的CUDA设备，将使用CPU")
                process_device = "cpu"
        
        print(f"进程 {os.getpid()} 开始处理专利 {patent_dir} 在设备 {device}")
        
        # 创建结果管理器（每个专利一个新的管理器）
        result_manager = ResultManager(output_config=output_config)
        
        # 获取或创建处理器（进程共享）
        processor = get_or_create_processor(process_device, process_id, output_config, model_paths)
        
        # 更新处理器的结果管理器为新的
        processor.result_manager = result_manager
        
        # 创建并运行专利处理器
        patent_processor = PatentProcessor(
            patent_dir=patent_dir,
            result_manager=result_manager,  # 使用专利特定的结果管理器
            device=process_device,
            processor=processor  # 使用共享处理器（已更新结果管理器）
        )
        success = patent_processor.process()
        
        # 如果处理成功，保存结果
        if success:
            # 创建特定于专利的输出目录
            patent_output_dir = Path(output_root) / f"{patent_dir.name}"
            patent_output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存所有结果
            result_manager.save_results(patent_output_dir)
            
            # 如果配置了，保存Excel
            if output_config.get("excel_results", True):
                excel_file = patent_output_dir / f"{patent_dir.name}_chemicals.xlsx"
                patent_processor.write_to_excel(excel_file)
        
        # 清理资源管理器的内存
        if result_manager is not None:
            result_manager.patent_results.clear()
            result_manager.image_results.clear()
            result_manager.molecule_results.clear()
            result_manager.reaction_results.clear()
            result_manager.experiment_results.clear()
            result_manager.visualization_data.clear()
            result_manager.molscribe_results.clear()
        
        # 强制进行垃圾回收
        gc.collect()
        
        # 清理GPU缓存
        if process_device.startswith('cuda') and torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return success
    except Exception as e:
        print(f"专利处理失败：{patent_dir.name} - {str(e)}")
        return False

if __name__ == "__main__":
    # 使用示例
    input_dir = "/home/<USER>/zhouxingyu/zxy_extractor/data/Patent_CN/parse_res"
    output_dir = "/home/<USER>/zhouxingyu/zxy_extractor/data/output"
    devices = ["cuda:0", "cuda:1", "cuda:2", "cuda:3"]
    output_config = {
        "json_results": True,
        "excel_results": True,
        "visualization": True,
        "intermediate_files": False
    }
    processes_per_gpu = 3
    processor = FolderProcessor(
        input_root=input_dir,
        output_root=output_dir,
        devices=devices,
        output_config=output_config,
        processes_per_gpu=processes_per_gpu
    )
    # 处理所有专利
    start_time = time.time()    
    processor.process()
    elapsed_time = time.time() - start_time
    print(f"总处理时间：{elapsed_time:.2f}秒")