#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MinerU客户端示例
演示如何使用MinerU服务将文档转换为Markdown
"""

import os
import sys
import base64
import json
import argparse
import requests
from pathlib import Path
from typing import Dict, Any, Optional, Union

def to_base64(file_path: Union[str, Path]) -> str:
    """
    将文件内容转换为Base64编码
    
    Args:
        file_path: 文件路径
        
    Returns:
        str: Base64编码的文件内容
    """
    try:
        with open(file_path, 'rb') as f:
            return base64.b64encode(f.read()).decode('utf-8')
    except Exception as e:
        raise Exception(f'文件: {file_path} - 错误: {e}')

def convert_document(
    file_path: Union[str, Path],
    server_url: str = 'http://127.0.0.1:8010',
    request_id: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    将文档转换为Markdown
    
    Args:
        file_path: 文档文件路径
        server_url: 服务器URL
        request_id: 请求ID（可选）
        **kwargs: 其他选项
        
    Returns:
        Dict[str, Any]: 服务器响应
    """
    # 构建请求URL
    url = f"{server_url}/predict"
    
    # 构建请求数据
    data = {
        'file': to_base64(file_path),
        'kwargs': kwargs
    }
    
    # 如果提供了请求ID，添加到请求中
    if request_id:
        data['request_id'] = request_id
    
    # 发送请求
    print(f"正在发送请求到 {url}")
    response = requests.post(url, json=data)
    
    # 检查响应状态
    if response.status_code != 200:
        print(f"错误: {response.status_code} - {response.text}")
        raise Exception(f"请求失败: {response.text}")
    
    # 解析响应
    result = response.json()
    print(f"请求成功: {result}")
    
    return result

def download_markdown(
    result: Dict[str, Any],
    server_url: str = 'http://127.0.0.1:8010',
    output_path: Optional[Union[str, Path]] = None
) -> Optional[Path]:
    """
    下载转换后的Markdown文件
    
    Args:
        result: 转换请求的响应
        server_url: 服务器URL
        output_path: 输出文件路径（可选）
        
    Returns:
        Optional[Path]: 保存的Markdown文件路径，如果失败则返回None
    """
    # 获取请求ID和输出目录
    request_id = result.get('request_id')
    if not request_id:
        print("错误: 响应中没有请求ID")
        return None
    
    # 构建Markdown文件URL
    md_url = f"{server_url}/files/{result['output_dir']}/auto/{request_id}.md"
    
    # 如果没有指定输出路径，使用当前目录和请求ID
    if not output_path:
        output_path = Path(f"{request_id}.md")
    else:
        output_path = Path(output_path)
    
    # 下载Markdown文件
    print(f"正在下载Markdown文件: {md_url}")
    try:
        response = requests.get(md_url)
        if response.status_code != 200:
            print(f"错误: {response.status_code} - {response.text}")
            return None
        
        # 保存文件
        with open(output_path, 'wb') as f:
            f.write(response.content)
        
        print(f"Markdown文件已保存到: {output_path}")
        return output_path
    except Exception as e:
        print(f"下载Markdown文件失败: {e}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MinerU客户端示例')
    parser.add_argument('file', help='要转换的文档文件路径')
    parser.add_argument('--server', default='http://127.0.0.1:8010', help='服务器URL')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--request-id', help='请求ID')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.file):
        print(f"错误: 文件不存在: {args.file}")
        return 1
    
    try:
        # 转换文档
        result = convert_document(
            file_path=args.file,
            server_url=args.server,
            request_id=args.request_id,
            debug_able=args.debug
        )
        
        # 下载Markdown文件
        md_path = download_markdown(
            result=result,
            server_url=args.server,
            output_path=args.output
        )
        
        if md_path:
            print(f"文档转换成功: {md_path}")
            return 0
        else:
            print("文档转换失败")
            return 1
    except Exception as e:
        print(f"错误: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
