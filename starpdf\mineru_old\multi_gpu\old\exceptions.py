from typing import Optional, Dict, Any
from fastapi import HTTPException

class MineruException(Exception):
    """基础异常类，所有自定义异常的基类"""
    
    def __init__(self, message: str, status_code: int = 500, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典表示"""
        result = {
            "error": self.__class__.__name__,
            "message": self.message,
            "status_code": self.status_code
        }
        if self.details:
            result["details"] = self.details
        return result
    
    def to_http_exception(self) -> HTTPException:
        """转换为FastAPI的HTTPException"""
        return HTTPException(
            status_code=self.status_code,
            detail=self.to_dict()
        )


class FileTypeNotSupportedError(MineruException):
    """不支持的文件类型异常"""
    
    def __init__(self, file_type: str, supported_types: list):
        super().__init__(
            message=f"不支持的文件类型: {file_type}",
            status_code=400,
            details={
                "file_type": file_type,
                "supported_types": supported_types
            }
        )


class FileProcessingError(MineruException):
    """文件处理异常"""
    
    def __init__(self, message: str, file_name: Optional[str] = None, request_id: Optional[str] = None):
        details = {}
        if file_name:
            details["file_name"] = file_name
        if request_id:
            details["request_id"] = request_id
            
        super().__init__(
            message=message,
            status_code=500,
            details=details
        )


class ParserNotFoundError(MineruException):
    """解析器未找到异常"""
    
    def __init__(self, file_type: str):
        super().__init__(
            message=f"未找到适用于 {file_type} 的解析器",
            status_code=500,
            details={"file_type": file_type}
        )


class ConfigurationError(MineruException):
    """配置错误异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        details = {}
        if config_key:
            details["config_key"] = config_key
            
        super().__init__(
            message=message,
            status_code=500,
            details=details
        )


class ResourceNotFoundError(MineruException):
    """资源未找到异常"""
    
    def __init__(self, resource_type: str, resource_id: str):
        super().__init__(
            message=f"未找到{resource_type}: {resource_id}",
            status_code=404,
            details={
                "resource_type": resource_type,
                "resource_id": resource_id
            }
        )
