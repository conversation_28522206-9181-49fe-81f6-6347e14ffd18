import logging
import os
from pathlib import Path
from typing import Optional, Dict, Any
from .config import config

class LoggerManager:
    """日志管理器，负责配置和提供日志记录器"""

    _loggers: Dict[str, logging.Logger] = {}
    _initialized = False

    @classmethod
    def setup(cls) -> None:
        """设置全局日志配置"""
        if cls._initialized:
            return

        # 获取日志配置
        log_level = getattr(logging, config.get("logging.level", "INFO"))
        log_format = config.get("logging.format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        log_file = config.get("logging.file")
        console_enabled = config.get("logging.console", True)

        # 创建根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)

        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建格式化器
        formatter = logging.Formatter(log_format)

        # 添加文件处理器
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)

        # 添加控制台处理器
        if console_enabled:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)

        cls._initialized = True

        # 创建并获取日志记录器
        logger = cls.get_logger("logger_manager")
        logger.info("日志系统初始化完成")

    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """
        获取或创建命名日志记录器

        Args:
            name: 日志记录器名称

        Returns:
            logging.Logger: 日志记录器实例
        """
        if name not in cls._loggers:
            logger = logging.getLogger(name)
            cls._loggers[name] = logger

        return cls._loggers[name]

# 初始化日志系统
LoggerManager.setup()

def get_logger(name: str) -> logging.Logger:
    """
    获取日志记录器的便捷函数

    Args:
        name: 日志记录器名称

    Returns:
        logging.Logger: 日志记录器实例
    """
    return LoggerManager.get_logger(name)
