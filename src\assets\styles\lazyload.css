/**
 * 图片懒加载样式
 */

/* 所有使用v-lazyload指令的图片 */
[v-lazyload] {
  min-height: 100px;
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  transition: opacity 0.3s ease-in-out, filter 0.3s ease-in-out;
  opacity: 0.5;
  filter: blur(5px);
}

/* 加载成功的图片 */
[v-lazyload].lazy-loaded {
  opacity: 1;
  filter: blur(0);
}

/* 加载失败的图片 */
[v-lazyload].lazy-error {
  opacity: 0.7;
  filter: grayscale(100%);
}
