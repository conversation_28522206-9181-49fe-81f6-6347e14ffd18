import logging
import os
import sys
from paddlex import create_pipeline

def run_ocr(image_path):
    try:
        pipeline = create_pipeline(
            pipeline="/home/<USER>/zhouxingyu/openchemie/lib/Paddle/OCR.yaml",
            device = "gpu:2")
        output = pipeline.predict(
            input=image_path,
            use_textline_orientation=False,
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
        )
        rec_texts = []

        for res in output:
            current_texts = res.get('rec_texts', [])
            if current_texts is None:
                current_texts = []
            rec_texts.extend(current_texts)
            # 可视化结果保存
            res.save_to_img(save_path="/home/<USER>/zhouxingyu/openchemie/lib/Paddle/")
            res.save_to_json(save_path="/home/<USER>/zhouxingyu/openchemie/lib/Paddle/")

        # 拼接为字符串返回
        return ','.join(rec_texts)

    except Exception as e:
        print(f"Error during OCR processing: {e}", file=sys.stderr)
        return ""

if __name__ == "__main__":
    os.environ['FLAGS_call_stack_level'] = '2'
    os.environ['NCCL_DEBUG'] = 'WARN'
    logging.disable(logging.CRITICAL)
    sys.stderr = open(os.devnull, 'w')

    image_path = sys.argv[1]
    # image_path = "/home/<USER>/zhouxingyu/openchemie/lib/Paddle/image.png"
    texts = run_ocr(image_path)

    print(f"paddleocr--------------:{texts}")