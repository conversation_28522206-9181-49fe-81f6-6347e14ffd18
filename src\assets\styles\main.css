@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义全局样式 */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  @apply bg-gray-50 text-gray-900;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* 自定义表单元素 */
input, select, textarea {
  @apply focus:ring-blue-500 focus:border-blue-500;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-blue-500;
}

.btn-danger {
  @apply text-white bg-red-600 hover:bg-red-700 focus:ring-red-500;
}

.btn-success {
  @apply text-white bg-green-600 hover:bg-green-700 focus:ring-green-500;
}

/* 卡片样式 */
.card {
  @apply bg-white shadow-md rounded-lg overflow-hidden;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.card-body {
  @apply p-6;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200;
}

/* 标签页样式 */
.tab-active {
  @apply border-blue-500 text-blue-600;
}

.tab-inactive {
  @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300;
}

/* 表格样式 */
.table-header {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* 状态标签样式 */
.status-badge {
  @apply px-2 inline-flex text-xs leading-5 font-semibold rounded-full;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800;
}

.status-processing {
  @apply bg-blue-100 text-blue-800;
}

.status-completed {
  @apply bg-green-100 text-green-800;
}

.status-failed {
  @apply bg-red-100 text-red-800;
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式工具类 */
.container {
  @apply mx-auto px-4 sm:px-6 lg:px-8;
}

/* 分子结构卡片 */
.molecule-card {
  @apply border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow;
}

.molecule-image {
  @apply h-32 flex items-center justify-center bg-gray-100 rounded;
}

.molecule-info {
  @apply text-xs text-gray-700;
}

/* 反应结构卡片 */
.reaction-card {
  @apply border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow;
}

.reaction-image {
  @apply h-40 flex items-center justify-center bg-gray-100 rounded;
}

.reaction-info {
  @apply text-xs text-gray-700;
}
