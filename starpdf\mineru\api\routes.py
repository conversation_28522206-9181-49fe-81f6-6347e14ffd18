import os
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional

import litserve as ls

from mineru.utils.config import config
from mineru.utils.logger import get_logger
from mineru.core.mineru_api import MinerUAPI

# 初始化日志
logger = get_logger("mineru_server")

# 请求模型
class PredictRequest(BaseModel):
    file: str
    kwargs: Optional[Dict[str, Any]] = {}
    request_id: Optional[str] = None  # 客户端可以提供请求ID用于跟踪

# 创建FastAPI应用
app = FastAPI(title="MinerU Server", description="文档转换为Markdown的API服务")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建MinerUAPI实例
OUTPUT_DIR = config.get("output.base_dir")
mineru_api = MinerUAPI(output_dir=OUTPUT_DIR)

# 主函数
def main():
    import argparse

    parser = argparse.ArgumentParser(description='MinerU Server')
    parser.add_argument('--port', type=int, default=config.get("server.port"), help='服务器端口')
    parser.add_argument('--host', type=str, default=config.get("server.host"), help='服务器主机')
    parser.add_argument('--output-dir', type=str, default=config.get("output.base_dir"), help='输出目录')
    parser.add_argument('--gpu-devices', type=str, default=','.join(map(str, config.get("gpu.devices"))), help='GPU设备ID，用逗号分隔')
    parser.add_argument('--workers-per-device', type=int, default=config.get("server.workers_per_device"), help='每个设备的工作进程数')
    parser.add_argument('--timeout', action='store_true', help='启用超时')
    parser.add_argument('--config', type=str, help='配置文件路径')

    args = parser.parse_args()

    # 如果提供了配置文件，重新加载配置
    if args.config:
        from mineru.utils.config import Config
        Config(args.config)

    # 更新输出目录
    OUTPUT_DIR = os.path.abspath(args.output_dir)
    os.environ['MINERU_OUTPUT_DIR'] = OUTPUT_DIR
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 解析GPU设备
    gpu_devices = [int(device.strip()) for device in args.gpu_devices.split(',') if device.strip()]

    # 创建LitServer
    server = ls.LitServer(
        mineru_api,
        accelerator=config.get("gpu.accelerator"),
        devices=gpu_devices,
        workers_per_device=args.workers_per_device,
        timeout=args.timeout
    )

    # 检查路由
    @server.app.get("/ping")
    async def ping():
        return {"status": "ok", "message": "MinerU 服务器正在运行"}

    @server.app.get("/files")
    async def get_file(path: str):
        try:
            file_path = Path(path)
            if not file_path.exists() or not file_path.is_file():
                # 记录详细的文件路径信息，便于调试
                logger.error(f"文件不存在: {path}")
                logger.error(f"绝对路径: {os.path.abspath(path)}")
                logger.error(f"当前工作目录: {os.getcwd()}")

                # 尝试查找文件的其他可能位置
                if '/auto/' in path or '\\auto\\' in path:
                    # 尝试在父目录中查找
                    parent_path = str(file_path).replace('/auto/', '/').replace('\\auto\\', '\\')
                    parent_file_path = Path(parent_path)
                    if parent_file_path.exists() and parent_file_path.is_file():
                        logger.info(f"找到替代文件: {parent_file_path}")
                        return FileResponse(path=parent_file_path)

                # 尝试在 auto 目录中查找
                if not '/auto/' in path and not '\\auto\\' in path:
                    # 提取请求ID
                    parts = path.split('/')
                    if len(parts) > 0:
                        file_name = parts[-1]
                        dir_path = '/'.join(parts[:-1])

                        # 尝试在 auto 目录中查找
                        auto_path = f"{dir_path}/auto/{file_name}"
                        auto_file_path = Path(auto_path)
                        if auto_file_path.exists() and auto_file_path.is_file():
                            logger.info(f"找到替代文件: {auto_file_path}")
                            return FileResponse(path=auto_file_path)

                        # 尝试使用请求ID命名的文件
                        if file_name == 'output.md':
                            # 提取请求ID
                            request_id = parts[-2] if len(parts) > 1 else None
                            if request_id:
                                # 尝试在 auto 目录中查找使用请求ID命名的文件
                                id_path = f"{dir_path}/auto/{request_id}.md"
                                id_file_path = Path(id_path)
                                if id_file_path.exists() and id_file_path.is_file():
                                    logger.info(f"找到替代文件: {id_file_path}")
                                    return FileResponse(path=id_file_path)

                raise HTTPException(status_code=404, detail=f"文件不存在: {path}")

            if file_path.is_dir():
                logger.error(f"请求的路径是一个目录，而不是文件: {path}")
                raise HTTPException(status_code=400, detail=f"请求的路径是一个目录，而不是文件: {path}")

            return FileResponse(path=file_path)
        except Exception as e:
            logger.error(f"获取文件错误: {str(e)}")
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(status_code=500, detail=str(e))

    @server.app.get("/files/list")
    async def list_files(path: str):
        """
        列出目录中的所有文件和子目录

        参数:
            path: 目录路径

        返回:
            包含目录内容的JSON对象
        """
        try:
            dir_path = Path(path)
            if not dir_path.exists():
                logger.error(f"目录不存在: {path}")
                logger.error(f"绝对路径: {os.path.abspath(path)}")
                logger.error(f"当前工作目录: {os.getcwd()}")
                raise HTTPException(status_code=404, detail=f"目录不存在: {path}")

            if not dir_path.is_dir():
                logger.error(f"路径不是目录: {path}")
                raise HTTPException(status_code=400, detail=f"路径不是目录: {path}")

            files = []
            for item in dir_path.iterdir():
                try:
                    stat_info = item.stat()
                    files.append({
                        "name": item.name,
                        "isDirectory": item.is_dir(),
                        "size": stat_info.st_size if item.is_file() else 0,
                        "lastModified": stat_info.st_mtime,
                        "path": str(item.relative_to(dir_path))
                    })
                except Exception as e:
                    logger.error(f"获取文件信息错误: {item}, {str(e)}")
                    # 跳过有问题的文件，继续处理其他文件
                    continue

            return {
                "path": str(dir_path),
                "files": files,
                "total": len(files),
                "directories": sum(1 for f in files if f["isDirectory"]),
                "regularFiles": sum(1 for f in files if not f["isDirectory"])
            }
        except Exception as e:
            logger.error(f"列出目录错误: {str(e)}")
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(status_code=500, detail=str(e))

    # 启动服务器
    logger.info(f"启动 MinerU 服务器，监听 {args.host}:{args.port}")
    server.run(host=args.host, port=args.port)

if __name__ == '__main__':
    main()
