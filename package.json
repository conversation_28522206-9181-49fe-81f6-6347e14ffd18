{"name": "patent-extractor-client", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "server": "nodemon --ignore uploads/ --ignore public/ --ignore \"**/*.zip\" --ignore \"**/*.pdf\" --ignore \"**/*.jpg\" --ignore \"**/*.png\" server/server.js", "db:migrate": "node database/migrate.js", "db:backup": "node database/backup.js", "db:restore": "node database/restore.js", "db:manage": "node database/db-manager.js", "start": "node server.js"}, "dependencies": {"@kangc/v-md-editor": "^2.3.18", "archiver": "^7.0.1", "axios": "^0.27.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.0", "compression": "^1.7.4", "connect-history-api-fallback": "^2.0.0", "core-js": "^3.23.3", "cors": "^2.8.5", "dompurify": "^3.2.5", "dotenv": "^16.0.1", "express": "^4.18.1", "express-rate-limit": "^6.4.0", "extract-zip": "^2.0.1", "github-markdown-css": "^5.8.1", "helmet": "^5.1.0", "highlight.js": "^11.11.1", "iconv-lite": "^0.6.3", "jsonwebtoken": "^8.5.1", "jszip": "^3.10.1", "marked": "^15.0.9", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^2.3.3", "nodemailer": "^6.7.7", "prismjs": "^1.30.0", "rimraf": "^3.0.2", "serve-static": "^1.15.0", "tiktoken": "^1.0.21", "uuid": "^8.3.2", "vue": "^3.2.37", "vue-router": "^4.1.2", "vuex": "^4.0.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.27.0", "@babel/preset-env": "^7.26.9", "@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/typography": "^0.5.4", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/compiler-sfc": "^3.2.37", "autoprefixer": "^10.4.7", "eslint": "^8.19.0", "eslint-plugin-vue": "^9.2.0", "nodemon": "^2.0.19", "postcss": "^8.4.14", "tailwindcss": "^3.1.6"}}